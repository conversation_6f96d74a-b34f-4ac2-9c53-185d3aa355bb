{"version": 3, "sources": ["src_pages_test-interceptor_tsx-async.14870672088799754990.hot-update.js", "src/pages/test-interceptor.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/pages/test-interceptor.tsx',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='704337352692829406';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"src/pages/personal-center/index.tsx\"],\"src/pages/team-management/index.tsx\":[\"src/pages/team-management/index.tsx\"],\"src/pages/test-interceptor.tsx\":[\"src/pages/test-interceptor.tsx\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * 测试拦截器页面\n * 用于测试响应拦截器是否正常工作\n */\n\nimport React from 'react';\nimport { Button, Card, Space, Typography, message, App } from 'antd';\nimport { apiRequest } from '@/utils/request';\n\nconst { Title, Text } = Typography;\n\nconst TestInterceptorPage: React.FC = () => {\n  const testSuccess = async () => {\n    try {\n      // 测试成功响应\n      const response = await apiRequest.get('/test/success');\n      console.log('成功响应:', response);\n    } catch (error) {\n      console.error('请求失败:', error);\n    }\n  };\n\n  const test403Error = async () => {\n    try {\n      // 测试 403 错误响应 - 使用一个可能返回403的真实API\n      const response = await apiRequest.get('/api/teams/999999/members');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到 403 错误:', error);\n    }\n  };\n\n  const testRealTeamAccess = async () => {\n    try {\n      // 测试真实的团队访问，可能触发403错误\n      console.log('🧪 [测试] 开始测试真实团队访问...');\n      const response = await apiRequest.get('/api/teams/current');\n      console.log('✅ [测试] 团队访问成功:', response);\n    } catch (error) {\n      console.error('❌ [测试] 团队访问失败:', error);\n    }\n  };\n\n  const testMock403 = async () => {\n    try {\n      console.log('🧪 [测试] 开始模拟403错误响应...');\n\n      // 创建一个模拟的403响应\n      const mockResponse = new Response(\n        JSON.stringify({\n          code: 403,\n          message: \"您的账户已在此团队中被停用\",\n          data: null,\n          timestamp: new Date().toISOString()\n        }),\n        {\n          status: 200, // 后端返回HTTP 200，业务状态码在body中\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      // 手动触发响应拦截器\n      console.log('🧪 [测试] 手动触发响应拦截器...');\n      // 这里我们无法直接调用拦截器，所以改为测试真实API\n      const response = await apiRequest.get('/api/teams/999999/info');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('❌ [测试] 模拟403测试结果:', error);\n    }\n  };\n\n  const test401Error = async () => {\n    try {\n      // 测试 401 错误响应\n      const response = await apiRequest.get('/test/401');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到 401 错误:', error);\n    }\n  };\n\n  const testNetworkError = async () => {\n    try {\n      // 测试网络错误\n      const response = await apiRequest.get('/test/network-error');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到网络错误:', error);\n    }\n  };\n\n  const testMessageComponent = () => {\n    // 直接测试 message 组件是否正常工作\n    console.log('🧪 [测试] 直接测试 message 组件...');\n    message.success('✅ Message 组件测试 - 成功消息');\n    setTimeout(() => {\n      message.error('❌ Message 组件测试 - 错误消息');\n    }, 1000);\n    setTimeout(() => {\n      message.warning('⚠️ Message 组件测试 - 警告消息');\n    }, 2000);\n    setTimeout(() => {\n      message.info('ℹ️ Message 组件测试 - 信息消息');\n    }, 3000);\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>拦截器测试页面</Title>\n      <Text type=\"secondary\">\n        此页面用于测试请求响应拦截器是否正常工作。请打开浏览器开发者工具查看控制台输出和网络请求。\n      </Text>\n\n      <Card title=\"测试按钮\" style={{ marginTop: '24px' }}>\n        <Space direction=\"vertical\" size=\"middle\">\n          <Button type=\"primary\" onClick={testMessageComponent}>\n            🧪 测试 Message 组件 (直接测试)\n          </Button>\n\n          <Button type=\"primary\" onClick={testSuccess}>\n            测试成功响应 (200)\n          </Button>\n\n          <Button danger onClick={test403Error}>\n            测试 403 权限错误 (模拟API)\n          </Button>\n\n          <Button danger onClick={testMock403}>\n            测试 403 错误 (不存在的团队)\n          </Button>\n\n          <Button danger onClick={testRealTeamAccess}>\n            测试真实团队访问 (可能403)\n          </Button>\n\n          <Button danger onClick={test401Error}>\n            测试 401 认证错误\n          </Button>\n\n          <Button danger onClick={testNetworkError}>\n            测试网络错误\n          </Button>\n        </Space>\n      </Card>\n\n      <Card title=\"预期行为\" style={{ marginTop: '24px' }}>\n        <ul>\n          <li><strong>Message 组件测试</strong>: 应该依次显示成功、错误、警告、信息四种类型的消息提示</li>\n          <li><strong>403 错误</strong>: 应该显示错误消息 \"您的账户已在此团队中被停用\" 或类似的权限错误消息</li>\n          <li><strong>401 错误</strong>: 应该显示 \"登录已过期，请重新登录\" 并跳转到登录页</li>\n          <li><strong>网络错误</strong>: 应该显示相应的网络错误消息</li>\n          <li><strong>成功响应</strong>: 正常处理，不显示错误消息</li>\n        </ul>\n      </Card>\n\n      <Card title=\"🔧 修复说明\" style={{ marginTop: '24px' }}>\n        <p><strong>根本问题</strong>: Antd 5.x 中的 message 组件需要通过 App 组件来正确工作。</p>\n        <p><strong>解决方案</strong>: 已在 config.ts 中启用 appConfig 配置，现在 message 组件应该能正常显示。</p>\n        <p><strong>测试方法</strong>: 先点击 \"测试 Message 组件\" 按钮验证基础功能，再测试拦截器错误处理。</p>\n      </Card>\n    </div>\n  );\n};\n\nexport default TestInterceptorPage;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,kCACA;IACE,SAAS;;;;;;wCCmKb;;;2BAAA;;;;;;;mFAjKkB;yCAC4C;4CACnC;;;;;;;;;YAE3B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC,MAAM,sBAAgC;gBACpC,MAAM,cAAc;oBAClB,IAAI;wBACF,SAAS;wBACT,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;wBACtC,QAAQ,GAAG,CAAC,SAAS;oBACvB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,SAAS;oBACzB;gBACF;gBAEA,MAAM,eAAe;oBACnB,IAAI;wBACF,kCAAkC;wBAClC,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;wBACtC,QAAQ,GAAG,CAAC,YAAY;oBAC1B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,eAAe;oBAC/B;gBACF;gBAEA,MAAM,qBAAqB;oBACzB,IAAI;wBACF,sBAAsB;wBACtB,QAAQ,GAAG,CAAC;wBACZ,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;wBACtC,QAAQ,GAAG,CAAC,kBAAkB;oBAChC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kBAAkB;oBAClC;gBACF;gBAEA,MAAM,cAAc;oBAClB,IAAI;wBACF,QAAQ,GAAG,CAAC;wBAEZ,eAAe;wBACM,IAAI,SACvB,KAAK,SAAS,CAAC;4BACb,MAAM;4BACN,SAAS;4BACT,MAAM;4BACN,WAAW,IAAI,OAAO,WAAW;wBACnC,IACA;4BACE,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;wBACF;wBAGF,YAAY;wBACZ,QAAQ,GAAG,CAAC;wBACZ,4BAA4B;wBAC5B,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;wBACtC,QAAQ,GAAG,CAAC,YAAY;oBAC1B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,qBAAqB;oBACrC;gBACF;gBAEA,MAAM,eAAe;oBACnB,IAAI;wBACF,cAAc;wBACd,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;wBACtC,QAAQ,GAAG,CAAC,YAAY;oBAC1B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,eAAe;oBAC/B;gBACF;gBAEA,MAAM,mBAAmB;oBACvB,IAAI;wBACF,SAAS;wBACT,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;wBACtC,QAAQ,GAAG,CAAC,YAAY;oBAC1B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,YAAY;oBAC5B;gBACF;gBAEA,MAAM,uBAAuB;oBAC3B,wBAAwB;oBACxB,QAAQ,GAAG,CAAC;oBACZ,aAAO,CAAC,OAAO,CAAC;oBAChB,WAAW;wBACT,aAAO,CAAC,KAAK,CAAC;oBAChB,GAAG;oBACH,WAAW;wBACT,aAAO,CAAC,OAAO,CAAC;oBAClB,GAAG;oBACH,WAAW;wBACT,aAAO,CAAC,IAAI,CAAC;oBACf,GAAG;gBACL;gBAEA,qBACE,2BAAC;oBAAI,OAAO;wBAAE,SAAS;oBAAO;;sCAC5B,2BAAC;4BAAM,OAAO;sCAAG;;;;;;sCACjB,2BAAC;4BAAK,MAAK;sCAAY;;;;;;sCAIvB,2BAAC,UAAI;4BAAC,OAAM;4BAAO,OAAO;gCAAE,WAAW;4BAAO;sCAC5C,cAAA,2BAAC,WAAK;gCAAC,WAAU;gCAAW,MAAK;;kDAC/B,2BAAC,YAAM;wCAAC,MAAK;wCAAU,SAAS;kDAAsB;;;;;;kDAItD,2BAAC,YAAM;wCAAC,MAAK;wCAAU,SAAS;kDAAa;;;;;;kDAI7C,2BAAC,YAAM;wCAAC,MAAM;wCAAC,SAAS;kDAAc;;;;;;kDAItC,2BAAC,YAAM;wCAAC,MAAM;wCAAC,SAAS;kDAAa;;;;;;kDAIrC,2BAAC,YAAM;wCAAC,MAAM;wCAAC,SAAS;kDAAoB;;;;;;kDAI5C,2BAAC,YAAM;wCAAC,MAAM;wCAAC,SAAS;kDAAc;;;;;;kDAItC,2BAAC,YAAM;wCAAC,MAAM;wCAAC,SAAS;kDAAkB;;;;;;;;;;;;;;;;;sCAM9C,2BAAC,UAAI;4BAAC,OAAM;4BAAO,OAAO;gCAAE,WAAW;4BAAO;sCAC5C,cAAA,2BAAC;;kDACC,2BAAC;;0DAAG,2BAAC;0DAAO;;;;;;4CAAqB;;;;;;;kDACjC,2BAAC;;0DAAG,2BAAC;0DAAO;;;;;;4CAAe;;;;;;;kDAC3B,2BAAC;;0DAAG,2BAAC;0DAAO;;;;;;4CAAe;;;;;;;kDAC3B,2BAAC;;0DAAG,2BAAC;0DAAO;;;;;;4CAAa;;;;;;;kDACzB,2BAAC;;0DAAG,2BAAC;0DAAO;;;;;;4CAAa;;;;;;;;;;;;;;;;;;sCAI7B,2BAAC,UAAI;4BAAC,OAAM;4BAAU,OAAO;gCAAE,WAAW;4BAAO;;8CAC/C,2BAAC;;sDAAE,2BAAC;sDAAO;;;;;;wCAAa;;;;;;;8CACxB,2BAAC;;sDAAE,2BAAC;sDAAO;;;;;;wCAAa;;;;;;;8CACxB,2BAAC;;sDAAE,2BAAC;sDAAO;;;;;;wCAAa;;;;;;;;;;;;;;;;;;;YAIhC;iBAzJM;gBA2JN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IDnKD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;SAAsC;QAAC,uCAAsC;YAAC;SAAsC;QAAC,kCAAiC;YAAC;SAAiC;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACjpB"}