((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['src/pages/test-interceptor.tsx'],
{ "src/pages/test-interceptor.tsx": function (module, exports, __mako_require__){
/**
 * 测试拦截器页面
 * 用于测试响应拦截器是否正常工作
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _request = __mako_require__("src/utils/request.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const { Title, Text } = _antd.Typography;
const TestInterceptorPage = ()=>{
    const testSuccess = async ()=>{
        try {
            // 测试成功响应
            const response = await _request.apiRequest.get('/test/success');
            console.log('成功响应:', response);
        } catch (error) {
            console.error('请求失败:', error);
        }
    };
    const test403Error = async ()=>{
        try {
            // 测试 403 错误响应 - 使用一个可能返回403的真实API
            const response = await _request.apiRequest.get('/api/teams/999999/members');
            console.log('不应该到达这里:', response);
        } catch (error) {
            console.error('捕获到 403 错误:', error);
        }
    };
    const testRealTeamAccess = async ()=>{
        try {
            // 测试真实的团队访问，可能触发403错误
            console.log('🧪 [测试] 开始测试真实团队访问...');
            const response = await _request.apiRequest.get('/api/teams/current');
            console.log('✅ [测试] 团队访问成功:', response);
        } catch (error) {
            console.error('❌ [测试] 团队访问失败:', error);
        }
    };
    const testMock403 = async ()=>{
        try {
            console.log('🧪 [测试] 开始模拟403错误响应...');
            // 创建一个模拟的403响应
            new Response(JSON.stringify({
                code: 403,
                message: "您的账户已在此团队中被停用",
                data: null,
                timestamp: new Date().toISOString()
            }), {
                status: 200,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            // 手动触发响应拦截器
            console.log('🧪 [测试] 手动触发响应拦截器...');
            // 这里我们无法直接调用拦截器，所以改为测试真实API
            const response = await _request.apiRequest.get('/api/teams/999999/info');
            console.log('不应该到达这里:', response);
        } catch (error) {
            console.error('❌ [测试] 模拟403测试结果:', error);
        }
    };
    const test401Error = async ()=>{
        try {
            // 测试 401 错误响应
            const response = await _request.apiRequest.get('/test/401');
            console.log('不应该到达这里:', response);
        } catch (error) {
            console.error('捕获到 401 错误:', error);
        }
    };
    const testNetworkError = async ()=>{
        try {
            // 测试网络错误
            const response = await _request.apiRequest.get('/test/network-error');
            console.log('不应该到达这里:', response);
        } catch (error) {
            console.error('捕获到网络错误:', error);
        }
    };
    const testMessageComponent = ()=>{
        // 直接测试 message 组件是否正常工作
        console.log('🧪 [测试] 直接测试 message 组件...');
        message.success('✅ Message 组件测试 - 成功消息');
        setTimeout(()=>{
            message.error('❌ Message 组件测试 - 错误消息');
        }, 1000);
        setTimeout(()=>{
            message.warning('⚠️ Message 组件测试 - 警告消息');
        }, 2000);
        setTimeout(()=>{
            message.info('ℹ️ Message 组件测试 - 信息消息');
        }, 3000);
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            padding: '24px'
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                level: 2,
                children: "拦截器测试页面"
            }, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 111,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                type: "secondary",
                children: "此页面用于测试请求响应拦截器是否正常工作。请打开浏览器开发者工具查看控制台输出和网络请求。"
            }, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 112,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: "测试按钮",
                style: {
                    marginTop: '24px'
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    direction: "vertical",
                    size: "middle",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            onClick: testMessageComponent,
                            children: "🧪 测试 Message 组件 (直接测试)"
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 118,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            type: "primary",
                            onClick: testSuccess,
                            children: "测试成功响应 (200)"
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 122,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            danger: true,
                            onClick: test403Error,
                            children: "测试 403 权限错误 (模拟API)"
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 126,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            danger: true,
                            onClick: testMock403,
                            children: "测试 403 错误 (不存在的团队)"
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 130,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            danger: true,
                            onClick: testRealTeamAccess,
                            children: "测试真实团队访问 (可能403)"
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 134,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            danger: true,
                            onClick: test401Error,
                            children: "测试 401 认证错误"
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 138,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                            danger: true,
                            onClick: testNetworkError,
                            children: "测试网络错误"
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 142,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/test-interceptor.tsx",
                    lineNumber: 117,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 116,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: "预期行为",
                style: {
                    marginTop: '24px'
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("ul", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                    children: "Message 组件测试"
                                }, void 0, false, {
                                    fileName: "src/pages/test-interceptor.tsx",
                                    lineNumber: 150,
                                    columnNumber: 15
                                }, this),
                                ": 应该依次显示成功、错误、警告、信息四种类型的消息提示"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 150,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                    children: "403 错误"
                                }, void 0, false, {
                                    fileName: "src/pages/test-interceptor.tsx",
                                    lineNumber: 151,
                                    columnNumber: 15
                                }, this),
                                ': 应该显示错误消息 "您的账户已在此团队中被停用" 或类似的权限错误消息'
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 151,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                    children: "401 错误"
                                }, void 0, false, {
                                    fileName: "src/pages/test-interceptor.tsx",
                                    lineNumber: 152,
                                    columnNumber: 15
                                }, this),
                                ': 应该显示 "登录已过期，请重新登录" 并跳转到登录页'
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 152,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                    children: "网络错误"
                                }, void 0, false, {
                                    fileName: "src/pages/test-interceptor.tsx",
                                    lineNumber: 153,
                                    columnNumber: 15
                                }, this),
                                ": 应该显示相应的网络错误消息"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 153,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("li", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                    children: "成功响应"
                                }, void 0, false, {
                                    fileName: "src/pages/test-interceptor.tsx",
                                    lineNumber: 154,
                                    columnNumber: 15
                                }, this),
                                ": 正常处理，不显示错误消息"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 154,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/test-interceptor.tsx",
                    lineNumber: 149,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 148,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                title: "🔧 修复说明",
                style: {
                    marginTop: '24px'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                children: "根本问题"
                            }, void 0, false, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 159,
                                columnNumber: 12
                            }, this),
                            ": Antd 5.x 中的 message 组件需要通过 App 组件来正确工作。"
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test-interceptor.tsx",
                        lineNumber: 159,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                children: "解决方案"
                            }, void 0, false, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 160,
                                columnNumber: 12
                            }, this),
                            ": 已在 config.ts 中启用 appConfig 配置，现在 message 组件应该能正常显示。"
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test-interceptor.tsx",
                        lineNumber: 160,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                children: "测试方法"
                            }, void 0, false, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 161,
                                columnNumber: 12
                            }, this),
                            ': 先点击 "测试 Message 组件" 按钮验证基础功能，再测试拦截器错误处理。'
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/test-interceptor.tsx",
                        lineNumber: 161,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/test-interceptor.tsx",
                lineNumber: 158,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/test-interceptor.tsx",
        lineNumber: 110,
        columnNumber: 5
    }, this);
};
_c = TestInterceptorPage;
var _default = TestInterceptorPage;
var _c;
$RefreshReg$(_c, "TestInterceptorPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_test-interceptor_tsx-async.js.map