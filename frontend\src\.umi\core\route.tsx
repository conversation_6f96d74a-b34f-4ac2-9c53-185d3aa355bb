// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/user","layout":false,"id":"1"},"2":{"name":"login","path":"/user/login","parentId":"1","id":"2"},"3":{"path":"/invite/:token","name":"团队邀请","layout":false,"id":"3"},"4":{"path":"/dashboard","name":"仪表盘","icon":"dashboard","parentId":"ant-design-pro-layout","id":"4"},"5":{"path":"/team-management","name":"团队管理","icon":"team","parentId":"ant-design-pro-layout","id":"5"},"6":{"path":"/personal-center","name":"个人中心","icon":"user","layout":false,"id":"6"},"7":{"path":"/help","name":"帮助中心","icon":"question","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"7"},"8":{"path":"/test-interceptor","name":"拦截器测试","icon":"experiment","hideInMenu":true,"parentId":"ant-design-pro-layout","id":"8"},"9":{"path":"/","redirect":"/dashboard","parentId":"ant-design-pro-layout","id":"9"},"10":{"path":"*","layout":false,"id":"10"},"ant-design-pro-layout":{"id":"ant-design-pro-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import('./EmptyRoute')),
'2': React.lazy(() => import(/* webpackChunkName: "p__user__login__index" */'@/pages/user/login/index.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__invite__token" */'@/pages/invite/[token].tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__Dashboard__index" */'@/pages/Dashboard/index.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__team-management__index" */'@/pages/team-management/index.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "p__personal-center__index" */'@/pages/personal-center/index.tsx')),
'7': React.lazy(() => import(/* webpackChunkName: "p__help__index" */'@/pages/help/index.tsx')),
'8': React.lazy(() => import(/* webpackChunkName: "p__test-interceptor" */'@/pages/test-interceptor.tsx')),
'9': React.lazy(() => import('./EmptyRoute')),
'10': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.tsx')),
'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: "umi__plugin-layout__Layout" */'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-layout/Layout.tsx')),
},
  };
}
