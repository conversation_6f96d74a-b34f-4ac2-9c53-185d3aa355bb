{"version": 3, "sources": ["src/pages/test-interceptor.tsx"], "sourcesContent": ["/**\n * 测试拦截器页面\n * 用于测试响应拦截器是否正常工作\n */\n\nimport React from 'react';\nimport { Button, Card, Space, Typography, message, App } from 'antd';\nimport { apiRequest } from '@/utils/request';\n\nconst { Title, Text } = Typography;\n\nconst TestInterceptorPage: React.FC = () => {\n  const { message: messageApi } = App.useApp();\n  const testSuccess = async () => {\n    try {\n      // 测试成功响应\n      const response = await apiRequest.get('/test/success');\n      console.log('成功响应:', response);\n    } catch (error) {\n      console.error('请求失败:', error);\n    }\n  };\n\n  const test403Error = async () => {\n    try {\n      // 测试 403 错误响应 - 使用一个可能返回403的真实API\n      const response = await apiRequest.get('/api/teams/999999/members');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到 403 错误:', error);\n    }\n  };\n\n  const testRealTeamAccess = async () => {\n    try {\n      // 测试真实的团队访问，可能触发403错误\n      console.log('🧪 [测试] 开始测试真实团队访问...');\n      const response = await apiRequest.get('/api/teams/current');\n      console.log('✅ [测试] 团队访问成功:', response);\n    } catch (error) {\n      console.error('❌ [测试] 团队访问失败:', error);\n    }\n  };\n\n  const testMock403 = async () => {\n    try {\n      console.log('🧪 [测试] 开始模拟403错误响应...');\n\n      // 创建一个模拟的403响应\n      const mockResponse = new Response(\n        JSON.stringify({\n          code: 403,\n          message: \"您的账户已在此团队中被停用\",\n          data: null,\n          timestamp: new Date().toISOString()\n        }),\n        {\n          status: 200, // 后端返回HTTP 200，业务状态码在body中\n          headers: {\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      // 手动触发响应拦截器\n      console.log('🧪 [测试] 手动触发响应拦截器...');\n      // 这里我们无法直接调用拦截器，所以改为测试真实API\n      const response = await apiRequest.get('/api/teams/999999/info');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('❌ [测试] 模拟403测试结果:', error);\n    }\n  };\n\n  const test401Error = async () => {\n    try {\n      // 测试 401 错误响应\n      const response = await apiRequest.get('/test/401');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到 401 错误:', error);\n    }\n  };\n\n  const testNetworkError = async () => {\n    try {\n      // 测试网络错误\n      const response = await apiRequest.get('/test/network-error');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到网络错误:', error);\n    }\n  };\n\n  const testMessageComponent = () => {\n    // 直接测试 message 组件是否正常工作\n    console.log('🧪 [测试] 直接测试 message 组件...');\n\n    // 检查 DOM 中是否有 message 容器\n    const checkMessageContainer = () => {\n      const containers = document.querySelectorAll('[class*=\"ant-message\"]');\n      console.log('🔍 [DOM检查] 找到的 message 容器:', containers);\n      containers.forEach((container, index) => {\n        console.log(`🔍 [DOM检查] 容器 ${index}:`, container);\n        console.log(`🔍 [DOM检查] 容器样式:`, window.getComputedStyle(container));\n        console.log(`🔍 [DOM检查] 容器位置:`, container.getBoundingClientRect());\n      });\n\n      // 检查所有可能的 Antd 相关容器\n      const allAntContainers = document.querySelectorAll('[class*=\"ant-\"]');\n      console.log('🔍 [DOM检查] 所有 Antd 容器数量:', allAntContainers.length);\n    };\n\n    // 测试静态方法\n    console.log('🧪 [测试] 测试静态方法 message.success...');\n    message.success('✅ 静态方法 - 成功消息');\n\n    // 检查 DOM\n    setTimeout(() => {\n      console.log('🔍 [DOM检查] 调用 message.success 后检查 DOM...');\n      checkMessageContainer();\n    }, 100);\n\n    // 测试 hooks 方法\n    console.log('🧪 [测试] 测试 hooks 方法 messageApi.success...');\n    messageApi.success('✅ Hooks 方法 - 成功消息');\n\n    setTimeout(() => {\n      console.log('🔍 [DOM检查] 调用 messageApi.success 后检查 DOM...');\n      checkMessageContainer();\n    }, 200);\n\n    setTimeout(() => {\n      console.log('🧪 [测试] 测试错误消息...');\n      message.error('❌ 静态方法 - 错误消息');\n      messageApi.error('❌ Hooks 方法 - 错误消息');\n\n      setTimeout(() => {\n        console.log('🔍 [DOM检查] 调用 error 消息后检查 DOM...');\n        checkMessageContainer();\n      }, 100);\n    }, 1000);\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>拦截器测试页面</Title>\n      <Text type=\"secondary\">\n        此页面用于测试请求响应拦截器是否正常工作。请打开浏览器开发者工具查看控制台输出和网络请求。\n      </Text>\n\n      <Card title=\"测试按钮\" style={{ marginTop: '24px' }}>\n        <Space direction=\"vertical\" size=\"middle\">\n          <Button type=\"primary\" onClick={testMessageComponent}>\n            🧪 测试 Message 组件 (直接测试)\n          </Button>\n\n          <Button type=\"primary\" onClick={testSuccess}>\n            测试成功响应 (200)\n          </Button>\n\n          <Button danger onClick={test403Error}>\n            测试 403 权限错误 (模拟API)\n          </Button>\n\n          <Button danger onClick={testMock403}>\n            测试 403 错误 (不存在的团队)\n          </Button>\n\n          <Button danger onClick={testRealTeamAccess}>\n            测试真实团队访问 (可能403)\n          </Button>\n\n          <Button danger onClick={test401Error}>\n            测试 401 认证错误\n          </Button>\n\n          <Button danger onClick={testNetworkError}>\n            测试网络错误\n          </Button>\n        </Space>\n      </Card>\n\n      <Card title=\"预期行为\" style={{ marginTop: '24px' }}>\n        <ul>\n          <li><strong>Message 组件测试</strong>: 应该依次显示成功、错误、警告、信息四种类型的消息提示</li>\n          <li><strong>403 错误</strong>: 应该显示错误消息 \"您的账户已在此团队中被停用\" 或类似的权限错误消息</li>\n          <li><strong>401 错误</strong>: 应该显示 \"登录已过期，请重新登录\" 并跳转到登录页</li>\n          <li><strong>网络错误</strong>: 应该显示相应的网络错误消息</li>\n          <li><strong>成功响应</strong>: 正常处理，不显示错误消息</li>\n        </ul>\n      </Card>\n\n      <Card title=\"🔧 修复说明\" style={{ marginTop: '24px' }}>\n        <p><strong>根本问题</strong>: Antd 5.x 中的 message 组件需要通过 App 组件来正确工作。</p>\n        <p><strong>解决方案</strong>: 已在 config.ts 中启用 appConfig 配置，现在 message 组件应该能正常显示。</p>\n        <p><strong>测试方法</strong>: 先点击 \"测试 Message 组件\" 按钮验证基础功能，再测试拦截器错误处理。</p>\n      </Card>\n    </div>\n  );\n};\n\nexport default TestInterceptorPage;\n"], "names": [], "mappings": ";;;AAAA;;;CAGC;;;;4BAuMD;;;eAAA;;;;;;;uEArMkB;6BAC4C;gCACnC;;;;;;;;;;AAE3B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,MAAM,sBAAgC;;IACpC,MAAM,EAAE,SAAS,UAAU,EAAE,GAAG,SAAG,CAAC,MAAM;IAC1C,MAAM,cAAc;QAClB,IAAI;YACF,SAAS;YACT,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,SAAS;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,YAAY;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC/B;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,sBAAsB;YACtB,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,kBAAkB;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,eAAe;YACM,IAAI,SACvB,KAAK,SAAS,CAAC;gBACb,MAAM;gBACN,SAAS;gBACT,MAAM;gBACN,WAAW,IAAI,OAAO,WAAW;YACnC,IACA;gBACE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAGF,YAAY;YACZ,QAAQ,GAAG,CAAC;YACZ,4BAA4B;YAC5B,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,YAAY;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,YAAY;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC/B;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,SAAS;YACT,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,YAAY;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;QAC5B;IACF;IAEA,MAAM,uBAAuB;QAC3B,wBAAwB;QACxB,QAAQ,GAAG,CAAC;QAEZ,yBAAyB;QACzB,MAAM,wBAAwB;YAC5B,MAAM,aAAa,SAAS,gBAAgB,CAAC;YAC7C,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,WAAW,OAAO,CAAC,CAAC,WAAW;gBAC7B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC,EAAE;gBACvC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,CAAC,EAAE,OAAO,gBAAgB,CAAC;gBACxD,QAAQ,GAAG,CAAC,CAAC,gBAAgB,CAAC,EAAE,UAAU,qBAAqB;YACjE;YAEA,oBAAoB;YACpB,MAAM,mBAAmB,SAAS,gBAAgB,CAAC;YACnD,QAAQ,GAAG,CAAC,4BAA4B,iBAAiB,MAAM;QACjE;QAEA,SAAS;QACT,QAAQ,GAAG,CAAC;QACZ,aAAO,CAAC,OAAO,CAAC;QAEhB,SAAS;QACT,WAAW;YACT,QAAQ,GAAG,CAAC;YACZ;QACF,GAAG;QAEH,cAAc;QACd,QAAQ,GAAG,CAAC;QACZ,WAAW,OAAO,CAAC;QAEnB,WAAW;YACT,QAAQ,GAAG,CAAC;YACZ;QACF,GAAG;QAEH,WAAW;YACT,QAAQ,GAAG,CAAC;YACZ,aAAO,CAAC,KAAK,CAAC;YACd,WAAW,KAAK,CAAC;YAEjB,WAAW;gBACT,QAAQ,GAAG,CAAC;gBACZ;YACF,GAAG;QACL,GAAG;IACL;IAEA,qBACE,2BAAC;QAAI,OAAO;YAAE,SAAS;QAAO;;0BAC5B,2BAAC;gBAAM,OAAO;0BAAG;;;;;;0BACjB,2BAAC;gBAAK,MAAK;0BAAY;;;;;;0BAIvB,2BAAC,UAAI;gBAAC,OAAM;gBAAO,OAAO;oBAAE,WAAW;gBAAO;0BAC5C,cAAA,2BAAC,WAAK;oBAAC,WAAU;oBAAW,MAAK;;sCAC/B,2BAAC,YAAM;4BAAC,MAAK;4BAAU,SAAS;sCAAsB;;;;;;sCAItD,2BAAC,YAAM;4BAAC,MAAK;4BAAU,SAAS;sCAAa;;;;;;sCAI7C,2BAAC,YAAM;4BAAC,MAAM;4BAAC,SAAS;sCAAc;;;;;;sCAItC,2BAAC,YAAM;4BAAC,MAAM;4BAAC,SAAS;sCAAa;;;;;;sCAIrC,2BAAC,YAAM;4BAAC,MAAM;4BAAC,SAAS;sCAAoB;;;;;;sCAI5C,2BAAC,YAAM;4BAAC,MAAM;4BAAC,SAAS;sCAAc;;;;;;sCAItC,2BAAC,YAAM;4BAAC,MAAM;4BAAC,SAAS;sCAAkB;;;;;;;;;;;;;;;;;0BAM9C,2BAAC,UAAI;gBAAC,OAAM;gBAAO,OAAO;oBAAE,WAAW;gBAAO;0BAC5C,cAAA,2BAAC;;sCACC,2BAAC;;8CAAG,2BAAC;8CAAO;;;;;;gCAAqB;;;;;;;sCACjC,2BAAC;;8CAAG,2BAAC;8CAAO;;;;;;gCAAe;;;;;;;sCAC3B,2BAAC;;8CAAG,2BAAC;8CAAO;;;;;;gCAAe;;;;;;;sCAC3B,2BAAC;;8CAAG,2BAAC;8CAAO;;;;;;gCAAa;;;;;;;sCACzB,2BAAC;;8CAAG,2BAAC;8CAAO;;;;;;gCAAa;;;;;;;;;;;;;;;;;;0BAI7B,2BAAC,UAAI;gBAAC,OAAM;gBAAU,OAAO;oBAAE,WAAW;gBAAO;;kCAC/C,2BAAC;;0CAAE,2BAAC;0CAAO;;;;;;4BAAa;;;;;;;kCACxB,2BAAC;;0CAAE,2BAAC;0CAAO;;;;;;4BAAa;;;;;;;kCACxB,2BAAC;;0CAAE,2BAAC;0CAAO;;;;;;4BAAa;;;;;;;;;;;;;;;;;;;AAIhC;GA7LM;;QAC4B,SAAG,CAAC;;;KADhC;IA+LN,WAAe"}