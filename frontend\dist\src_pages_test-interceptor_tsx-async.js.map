{"version": 3, "sources": ["src/pages/test-interceptor.tsx"], "sourcesContent": ["/**\n * 测试拦截器页面\n * 用于测试响应拦截器是否正常工作\n */\n\nimport React from 'react';\nimport { <PERSON>ton, Card, Space, Typography } from 'antd';\nimport { apiRequest } from '@/utils/request';\n\nconst { Title, Text } = Typography;\n\nconst TestInterceptorPage: React.FC = () => {\n  const testSuccess = async () => {\n    try {\n      // 测试成功响应\n      const response = await apiRequest.get('/test/success');\n      console.log('成功响应:', response);\n    } catch (error) {\n      console.error('请求失败:', error);\n    }\n  };\n\n  const test403Error = async () => {\n    try {\n      // 测试 403 错误响应\n      const response = await apiRequest.get('/test/403');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到 403 错误:', error);\n    }\n  };\n\n  const test401Error = async () => {\n    try {\n      // 测试 401 错误响应\n      const response = await apiRequest.get('/test/401');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到 401 错误:', error);\n    }\n  };\n\n  const testNetworkError = async () => {\n    try {\n      // 测试网络错误\n      const response = await apiRequest.get('/test/network-error');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到网络错误:', error);\n    }\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>拦截器测试页面</Title>\n      <Text type=\"secondary\">\n        此页面用于测试请求响应拦截器是否正常工作。请打开浏览器开发者工具查看控制台输出和网络请求。\n      </Text>\n\n      <Card title=\"测试按钮\" style={{ marginTop: '24px' }}>\n        <Space direction=\"vertical\" size=\"middle\">\n          <Button type=\"primary\" onClick={testSuccess}>\n            测试成功响应 (200)\n          </Button>\n          \n          <Button danger onClick={test403Error}>\n            测试 403 权限错误\n          </Button>\n          \n          <Button danger onClick={test401Error}>\n            测试 401 认证错误\n          </Button>\n          \n          <Button danger onClick={testNetworkError}>\n            测试网络错误\n          </Button>\n        </Space>\n      </Card>\n\n      <Card title=\"预期行为\" style={{ marginTop: '24px' }}>\n        <ul>\n          <li><strong>403 错误</strong>: 应该显示错误消息 \"您的账户已在此团队中被停用\" 或类似的权限错误消息</li>\n          <li><strong>401 错误</strong>: 应该显示 \"登录已过期，请重新登录\" 并跳转到登录页</li>\n          <li><strong>网络错误</strong>: 应该显示相应的网络错误消息</li>\n          <li><strong>成功响应</strong>: 正常处理，不显示错误消息</li>\n        </ul>\n      </Card>\n    </div>\n  );\n};\n\nexport default TestInterceptorPage;\n"], "names": [], "mappings": ";;;AAAA;;;CAGC;;;;4BAwFD;;;eAAA;;;;;;;yDAtFkB;6BAC8B;gCACrB;;;;;;;;;AAE3B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,MAAM,sBAAgC;IACpC,MAAM,cAAc;QAClB,IAAI;YAEF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,SAAS;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YAEF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,YAAY;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC/B;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YAEF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,YAAY;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC/B;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YAEF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,YAAY;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;QAC5B;IACF;IAEA,OACE,2BAAC;QAAI,OAAO;YAAE,SAAS;QAAO;;YAC5B,2BAAC;gBAAM,OAAO;0BAAG;;;;;;YACjB,2BAAC;gBAAK,MAAK;0BAAY;;;;;;YAIvB,2BAAC,UAAI;gBAAC,OAAM;gBAAO,OAAO;oBAAE,WAAW;gBAAO;0BAC5C,2BAAC,WAAK;oBAAC,WAAU;oBAAW,MAAK;;wBAC/B,2BAAC,YAAM;4BAAC,MAAK;4BAAU,SAAS;sCAAa;;;;;;wBAI7C,2BAAC,YAAM;4BAAC,MAAM;4BAAC,SAAS;sCAAc;;;;;;wBAItC,2BAAC,YAAM;4BAAC,MAAM;4BAAC,SAAS;sCAAc;;;;;;wBAItC,2BAAC,YAAM;4BAAC,MAAM;4BAAC,SAAS;sCAAkB;;;;;;;;;;;;;;;;;YAM9C,2BAAC,UAAI;gBAAC,OAAM;gBAAO,OAAO;oBAAE,WAAW;gBAAO;0BAC5C,2BAAC;;wBACC,2BAAC;;gCAAG,2BAAC;8CAAO;;;;;;gCAAe;;;;;;;wBAC3B,2BAAC;;gCAAG,2BAAC;8CAAO;;;;;;gCAAe;;;;;;;wBAC3B,2BAAC;;gCAAG,2BAAC;8CAAO;;;;;;gCAAa;;;;;;;wBACzB,2BAAC;;gCAAG,2BAAC;8CAAO;;;;;;gCAAa;;;;;;;;;;;;;;;;;;;;;;;;AAKnC;KA9EM;IAgFN,WAAe"}