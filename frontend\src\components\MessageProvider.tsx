/**
 * Message Provider 组件
 * 用于在应用启动时设置全局 message 实例
 */

import React, { useEffect } from 'react';
import { App } from 'antd';
import { setGlobalMessage } from '@/utils/request';

interface MessageProviderProps {
  children: React.ReactNode;
}

const MessageProvider: React.FC<MessageProviderProps> = ({ children }) => {
  const { message } = App.useApp();

  useEffect(() => {
    // 在组件挂载时设置全局 message 实例
    console.log('🔧 [MessageProvider] 设置全局 message 实例');
    setGlobalMessage(message);
    
    return () => {
      // 组件卸载时清理
      console.log('🔧 [MessageProvider] 清理全局 message 实例');
      setGlobalMessage(null);
    };
  }, [message]);

  return <>{children}</>;
};

export default MessageProvider;
