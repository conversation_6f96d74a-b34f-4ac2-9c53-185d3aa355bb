{"version": 3, "sources": ["umi.14589170155492038498.hot-update.js", "src/utils/request.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='12604516262876964104';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * 请求工具类\n * 基于 umi-request 封装，支持双阶段认证\n */\n\nimport { message } from 'antd';\nimport { extend } from 'umi-request';\nimport { history } from '@umijs/max';\nimport type { ApiResponse } from '@/types/api';\n\n// 创建请求实例\nconst request = extend({\n  prefix: '/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n/**\n * Token 管理器（单令牌系统）\n *\n * 功能说明：\n * - 统一管理用户认证Token的存储和获取\n * - 使用localStorage进行持久化存储\n * - 支持Token的设置、获取、清除和检查\n *\n * 设计理念：\n * - 采用单令牌系统，简化认证流程\n * - Token同时用于用户认证和团队访问\n * - 提供静态方法，便于全局调用\n */\nclass TokenManager {\n  private static readonly TOKEN_KEY = 'auth_token';\n\n  /**\n   * 获取当前Token\n   */\n  static getToken(): string | null {\n    return localStorage.getItem(TokenManager.TOKEN_KEY);\n  }\n\n  /**\n   * 设置Token\n   */\n  static setToken(token: string): void {\n    localStorage.setItem(TokenManager.TOKEN_KEY, token);\n  }\n\n  /**\n   * 清除Token\n   */\n  static clearToken(): void {\n    localStorage.removeItem(TokenManager.TOKEN_KEY);\n  }\n\n  /**\n   * 检查是否有Token\n   */\n  static hasToken(): boolean {\n    return !!TokenManager.getToken();\n  }\n}\n\n/**\n * 请求拦截器\n *\n * 功能：\n * - 自动在请求头中添加Authorization Bearer Token\n * - 统一处理认证信息的注入\n * - 支持无Token的公开接口访问\n */\nrequest.interceptors.request.use((url, options) => {\n  const token = TokenManager.getToken();\n\n  if (token) {\n    // 添加Authorization头部\n    const headers = {\n      ...options.headers,\n      Authorization: `Bearer ${token}`,\n    };\n    return {\n      url,\n      options: { ...options, headers },\n    };\n  }\n\n  return { url, options };\n});\n\n/**\n * 响应拦截器\n *\n * 功能：\n * - 统一处理API响应格式\n * - 自动处理认证失效情况\n * - 统一的错误消息提示\n * - 自动跳转到登录页面\n * - 支持多种错误显示方式\n */\nrequest.interceptors.response.use(async (response) => {\n  let data;\n  try {\n    data = await response.clone().json();\n  } catch (error) {\n    // 如果不是JSON格式，直接返回响应\n    return response;\n  }\n\n  // 检查业务状态码\n  if (data.code !== 200) {\n    // 认证失败的处理\n    if (data.code === 401) {\n      // 检查当前路径，如果是Dashboard相关页面且刚刚进行了团队切换，\n      // 可能是Token更新的时序问题，不立即跳转\n      const currentPath = window.location.pathname;\n      const isDashboardRelated =\n        currentPath.startsWith('/dashboard') ||\n        currentPath.startsWith('/team');\n\n      // 如果是Dashboard相关页面，延迟处理认证错误\n      if (isDashboardRelated) {\n        console.warn(\n          'Dashboard页面认证失败，可能是Token更新时序问题:',\n          data.message,\n        );\n        throw new Error(data.message);\n      }\n\n      // 其他页面立即处理认证错误\n      TokenManager.clearToken();\n      message.error('登录已过期，请重新登录');\n      // 跳转到登录页，避免重复跳转\n      if (window.location.pathname !== '/user/login') {\n        history.push('/user/login');\n      }\n      throw new Error(data.message);\n    }\n\n    // 处理权限错误\n    if (data.code === 403) {\n      // 显示后端返回的具体错误消息\n      message.error(data.message || '没有权限访问该资源');\n      throw new Error(data.message);\n    }\n\n    // 其他业务错误，显示错误消息\n    message.error(data.message || '请求失败');\n    throw new Error(data.message);\n  }\n\n  return response;\n});\n\n// 使用 umi-request 的错误处理中间件来处理网络错误\nrequest.use(async (ctx, next) => {\n  try {\n    await next();\n  } catch (error: any) {\n    console.log('网络错误处理中间件捕获到错误:', error);\n\n    // 网络错误或其他错误\n    if (error.response) {\n      const { status } = error.response;\n      console.log('HTTP错误状态码:', status);\n\n      if (status === 401) {\n        // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题\n        const currentPath = window.location.pathname;\n        const isDashboardRelated =\n          currentPath.startsWith('/dashboard') ||\n          currentPath.startsWith('/team');\n\n        if (isDashboardRelated) {\n          console.warn('Dashboard页面认证失败，可能是Token更新时序问题');\n          // 不立即清除Token和跳转，让页面自己处理\n          throw error;\n        }\n\n        // 其他页面立即处理认证错误\n        TokenManager.clearToken();\n        message.error('登录已过期，请重新登录');\n        if (window.location.pathname !== '/user/login') {\n          history.push('/user/login');\n        }\n      } else if (status === 403) {\n        // 检查是否是团队访问被拒绝的特殊错误\n        const errorMessage = error.response?.data?.message;\n        if (errorMessage?.includes('停用') || errorMessage?.includes('禁用') || errorMessage?.includes('不是该团队的成员')) {\n          // 团队访问相关的错误，使用后端返回的具体消息\n          message.error(errorMessage);\n        } else {\n          // 其他权限错误\n          message.error('没有权限访问该资源');\n        }\n      } else if (status === 404) {\n        message.error('请求的资源不存在');\n      } else if (status >= 500) {\n        message.error('服务器错误，请稍后重试');\n      } else {\n        message.error(`请求失败: ${status}`);\n      }\n    } else if (error.request) {\n      // 网络连接错误\n      message.error('网络错误，请检查网络连接');\n    } else {\n      // 其他错误\n      message.error('请求失败，请重试');\n    }\n\n    throw error;\n  }\n});\n\n// 封装常用的请求方法\nexport const apiRequest = {\n  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {\n    return request.get(url, { params });\n  },\n\n  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {\n    return request.post(url, { data });\n  },\n\n  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {\n    return request.put(url, { data });\n  },\n\n  delete: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {\n    return request.delete(url, { params });\n  },\n};\n\n// 导出 Token 管理器\nexport { TokenManager };\n\n// 导出默认请求实例\nexport default request;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCuOJ,YAAY;2BAAZ;;gBAnBI,UAAU;2BAAV;;gBAqBb,WAAW;gBACX,OAAuB;2BAAvB;;;;;yCAxOwB;+CACD;wCACC;;;;;;;;;YAGxB,SAAS;YACT,MAAM,UAAU,IAAA,kBAAM,EAAC;gBACrB,QAAQ;gBACR,SAAS;gBACT,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA;;;;;;;;;;;;CAYC,GACD,MAAM;gBACJ,OAAwB,YAAY,aAAa;gBAEjD;;GAEC,GACD,OAAO,WAA0B;oBAC/B,OAAO,aAAa,OAAO,CAAC,aAAa,SAAS;gBACpD;gBAEA;;GAEC,GACD,OAAO,SAAS,KAAa,EAAQ;oBACnC,aAAa,OAAO,CAAC,aAAa,SAAS,EAAE;gBAC/C;gBAEA;;GAEC,GACD,OAAO,aAAmB;oBACxB,aAAa,UAAU,CAAC,aAAa,SAAS;gBAChD;gBAEA;;GAEC,GACD,OAAO,WAAoB;oBACzB,OAAO,CAAC,CAAC,aAAa,QAAQ;gBAChC;YACF;YAEA;;;;;;;CAOC,GACD,QAAQ,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK;gBACrC,MAAM,QAAQ,aAAa,QAAQ;gBAEnC,IAAI,OAAO;oBACT,oBAAoB;oBACpB,MAAM,UAAU;wBACd,GAAG,QAAQ,OAAO;wBAClB,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC;oBAClC;oBACA,OAAO;wBACL;wBACA,SAAS;4BAAE,GAAG,OAAO;4BAAE;wBAAQ;oBACjC;gBACF;gBAEA,OAAO;oBAAE;oBAAK;gBAAQ;YACxB;YAEA;;;;;;;;;CASC,GACD,QAAQ,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO;gBACvC,IAAI;gBACJ,IAAI;oBACF,OAAO,MAAM,SAAS,KAAK,GAAG,IAAI;gBACpC,EAAE,OAAO,OAAO;oBACd,oBAAoB;oBACpB,OAAO;gBACT;gBAEA,UAAU;gBACV,IAAI,KAAK,IAAI,KAAK,KAAK;oBACrB,UAAU;oBACV,IAAI,KAAK,IAAI,KAAK,KAAK;wBACrB,qCAAqC;wBACrC,wBAAwB;wBACxB,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;wBAC5C,MAAM,qBACJ,YAAY,UAAU,CAAC,iBACvB,YAAY,UAAU,CAAC;wBAEzB,4BAA4B;wBAC5B,IAAI,oBAAoB;4BACtB,QAAQ,IAAI,CACV,mCACA,KAAK,OAAO;4BAEd,MAAM,IAAI,MAAM,KAAK,OAAO;wBAC9B;wBAEA,eAAe;wBACf,aAAa,UAAU;wBACvB,aAAO,CAAC,KAAK,CAAC;wBACd,gBAAgB;wBAChB,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,eAC/B,YAAO,CAAC,IAAI,CAAC;wBAEf,MAAM,IAAI,MAAM,KAAK,OAAO;oBAC9B;oBAEA,SAAS;oBACT,IAAI,KAAK,IAAI,KAAK,KAAK;wBACrB,gBAAgB;wBAChB,aAAO,CAAC,KAAK,CAAC,KAAK,OAAO,IAAI;wBAC9B,MAAM,IAAI,MAAM,KAAK,OAAO;oBAC9B;oBAEA,gBAAgB;oBAChB,aAAO,CAAC,KAAK,CAAC,KAAK,OAAO,IAAI;oBAC9B,MAAM,IAAI,MAAM,KAAK,OAAO;gBAC9B;gBAEA,OAAO;YACT;YAEA,iCAAiC;YACjC,QAAQ,GAAG,CAAC,OAAO,KAAK;gBACtB,IAAI;oBACF,MAAM;gBACR,EAAE,OAAO,OAAY;oBACnB,QAAQ,GAAG,CAAC,mBAAmB;oBAE/B,YAAY;oBACZ,IAAI,MAAM,QAAQ,EAAE;wBAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;wBACjC,QAAQ,GAAG,CAAC,cAAc;wBAE1B,IAAI,WAAW,KAAK;4BAClB,0CAA0C;4BAC1C,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;4BAC5C,MAAM,qBACJ,YAAY,UAAU,CAAC,iBACvB,YAAY,UAAU,CAAC;4BAEzB,IAAI,oBAAoB;gCACtB,QAAQ,IAAI,CAAC;gCACb,wBAAwB;gCACxB,MAAM;4BACR;4BAEA,eAAe;4BACf,aAAa,UAAU;4BACvB,aAAO,CAAC,KAAK,CAAC;4BACd,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,eAC/B,YAAO,CAAC,IAAI,CAAC;wBAEjB,OAAO,IAAI,WAAW,KAAK;gCAEJ,sBAAA;4BADrB,oBAAoB;4BACpB,MAAM,gBAAe,kBAAA,MAAM,QAAQ,cAAd,uCAAA,uBAAA,gBAAgB,IAAI,cAApB,2CAAA,qBAAsB,OAAO;4BAClD,IAAI,CAAA,yBAAA,mCAAA,aAAc,QAAQ,CAAC,WAAS,yBAAA,mCAAA,aAAc,QAAQ,CAAC,WAAS,yBAAA,mCAAA,aAAc,QAAQ,CAAC,cACzF,wBAAwB;4BACxB,aAAO,CAAC,KAAK,CAAC;iCAEd,SAAS;4BACT,aAAO,CAAC,KAAK,CAAC;wBAElB,OAAO,IAAI,WAAW,KACpB,aAAO,CAAC,KAAK,CAAC;6BACT,IAAI,UAAU,KACnB,aAAO,CAAC,KAAK,CAAC;6BAEd,aAAO,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC;oBAEnC,OAAO,IAAI,MAAM,OAAO,EACtB,SAAS;oBACT,aAAO,CAAC,KAAK,CAAC;yBAEd,OAAO;oBACP,aAAO,CAAC,KAAK,CAAC;oBAGhB,MAAM;gBACR;YACF;YAGO,MAAM,aAAa;gBACxB,KAAK,CAAU,KAAa;oBAC1B,OAAO,QAAQ,GAAG,CAAC,KAAK;wBAAE;oBAAO;gBACnC;gBAEA,MAAM,CAAU,KAAa;oBAC3B,OAAO,QAAQ,IAAI,CAAC,KAAK;wBAAE;oBAAK;gBAClC;gBAEA,KAAK,CAAU,KAAa;oBAC1B,OAAO,QAAQ,GAAG,CAAC,KAAK;wBAAE;oBAAK;gBACjC;gBAEA,QAAQ,CAAU,KAAa;oBAC7B,OAAO,QAAQ,MAAM,CAAC,KAAK;wBAAE;oBAAO;gBACtC;YACF;gBAMA,WAAe;;;;;;;;;;;;;;;;;;;;;;;ID1OD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}