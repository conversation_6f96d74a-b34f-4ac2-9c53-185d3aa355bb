/**
 * 请求工具类
 * 基于 umi-request 封装，支持双阶段认证
 */

import { message } from 'antd';
import { extend } from 'umi-request';
import { history } from '@umijs/max';
import type { ApiResponse } from '@/types/api';

// 创建请求实例
const request = extend({
  prefix: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Token 管理器（单令牌系统）
 *
 * 功能说明：
 * - 统一管理用户认证Token的存储和获取
 * - 使用localStorage进行持久化存储
 * - 支持Token的设置、获取、清除和检查
 *
 * 设计理念：
 * - 采用单令牌系统，简化认证流程
 * - Token同时用于用户认证和团队访问
 * - 提供静态方法，便于全局调用
 */
class TokenManager {
  private static readonly TOKEN_KEY = 'auth_token';

  /**
   * 获取当前Token
   */
  static getToken(): string | null {
    return localStorage.getItem(TokenManager.TOKEN_KEY);
  }

  /**
   * 设置Token
   */
  static setToken(token: string): void {
    localStorage.setItem(TokenManager.TOKEN_KEY, token);
  }

  /**
   * 清除Token
   */
  static clearToken(): void {
    localStorage.removeItem(TokenManager.TOKEN_KEY);
  }

  /**
   * 检查是否有Token
   */
  static hasToken(): boolean {
    return !!TokenManager.getToken();
  }
}

/**
 * 请求拦截器
 *
 * 功能：
 * - 自动在请求头中添加Authorization Bearer Token
 * - 统一处理认证信息的注入
 * - 支持无Token的公开接口访问
 */
request.interceptors.request.use((url, options) => {
  const token = TokenManager.getToken();

  if (token) {
    // 添加Authorization头部
    const headers = {
      ...options.headers,
      Authorization: `Bearer ${token}`,
    };
    return {
      url,
      options: { ...options, headers },
    };
  }

  return { url, options };
});

/**
 * 响应拦截器
 *
 * 功能：
 * - 统一处理API响应格式
 * - 自动处理认证失效情况
 * - 统一的错误消息提示
 * - 自动跳转到登录页面
 * - 支持多种错误显示方式
 */
request.interceptors.response.use(async (response) => {
  // 1. 响应拦截器触发的时间
  console.log('🔍 [响应拦截器] 触发时间:', new Date().toISOString());
  console.log('🔍 [响应拦截器] 请求URL:', response.url);

  // 2. 收到的 HTTP 状态码
  console.log('🔍 [响应拦截器] HTTP状态码:', response.status);
  console.log('🔍 [响应拦截器] HTTP状态文本:', response.statusText);
  console.log('🔍 [响应拦截器] Response对象:', response);

  let data;
  try {
    console.log('🔍 [响应拦截器] 开始解析JSON响应体...');
    data = await response.clone().json();

    // 3. 解析后的响应数据结构
    console.log('🔍 [响应拦截器] 解析成功，响应数据结构:', data);
    console.log('🔍 [响应拦截器] 响应数据类型:', typeof data);
    console.log('🔍 [响应拦截器] 响应数据键:', Object.keys(data || {}));
  } catch (error) {
    // 6. JSON 解析过程中出现的任何错误
    console.error('❌ [响应拦截器] JSON解析失败:', error);
    console.log('🔍 [响应拦截器] 响应Content-Type:', response.headers.get('content-type'));
    console.log('🔍 [响应拦截器] 响应体为非JSON格式，直接返回响应对象');
    return response;
  }

  // 4. 是否检测到业务错误代码 (data.code)
  console.log('🔍 [响应拦截器] 检查业务状态码 data.code:', data.code);
  console.log('🔍 [响应拦截器] data.code 类型:', typeof data.code);
  console.log('🔍 [响应拦截器] data.code !== 200 ?', data.code !== 200);

  // 检查业务状态码
  if (data.code !== 200) {
    console.log('⚠️ [响应拦截器] 检测到业务错误，code:', data.code);
    console.log('⚠️ [响应拦截器] 错误消息:', data.message);

    // 认证失败的处理
    if (data.code === 401) {
      console.log('🔐 [响应拦截器] 处理401认证错误');
      // 检查当前路径，如果是Dashboard相关页面且刚刚进行了团队切换，
      // 可能是Token更新的时序问题，不立即跳转
      const currentPath = window.location.pathname;
      const isDashboardRelated =
        currentPath.startsWith('/dashboard') ||
        currentPath.startsWith('/team');

      console.log('🔐 [响应拦截器] 当前路径:', currentPath);
      console.log('🔐 [响应拦截器] 是否Dashboard相关页面:', isDashboardRelated);

      // 如果是Dashboard相关页面，延迟处理认证错误
      if (isDashboardRelated) {
        console.warn(
          '🔐 [响应拦截器] Dashboard页面认证失败，可能是Token更新时序问题:',
          data.message,
        );
        throw new Error(data.message);
      }

      // 其他页面立即处理认证错误
      console.log('🔐 [响应拦截器] 清除Token并跳转到登录页');
      TokenManager.clearToken();

      // 5. 调用 message.error() 的时间
      console.log('📢 [响应拦截器] 调用message.error() 时间:', new Date().toISOString());
      console.log('📢 [响应拦截器] 显示401错误消息: "登录已过期，请重新登录"');
      message.error('登录已过期，请重新登录');

      // 跳转到登录页，避免重复跳转
      if (window.location.pathname !== '/user/login') {
        console.log('🔐 [响应拦截器] 跳转到登录页');
        history.push('/user/login');
      }
      throw new Error(data.message);
    }

    // 处理权限错误
    if (data.code === 403) {
      console.log('🚫 [响应拦截器] 处理403权限错误');
      console.log('🚫 [响应拦截器] 错误消息内容:', data.message);

      // 5. 调用 message.error() 的时间
      console.log('📢 [响应拦截器] 调用message.error() 时间:', new Date().toISOString());
      console.log('📢 [响应拦截器] 显示403错误消息:', data.message || '没有权限访问该资源');

      // 显示后端返回的具体错误消息
      message.error(data.message || '没有权限访问该资源');

      console.log('📢 [响应拦截器] message.error() 调用完成');
      console.log('🚫 [响应拦截器] 抛出403错误');
      throw new Error(data.message);
    }

    // 其他业务错误，显示错误消息
    console.log('❌ [响应拦截器] 处理其他业务错误，code:', data.code);

    // 5. 调用 message.error() 的时间
    console.log('📢 [响应拦截器] 调用message.error() 时间:', new Date().toISOString());
    console.log('📢 [响应拦截器] 显示其他错误消息:', data.message || '请求失败');

    message.error(data.message || '请求失败');

    console.log('📢 [响应拦截器] message.error() 调用完成');
    console.log('❌ [响应拦截器] 抛出业务错误');
    throw new Error(data.message);
  }

  console.log('✅ [响应拦截器] 响应正常，code: 200，返回响应对象');
  return response;
});

// 使用 umi-request 的错误处理中间件来处理网络错误
request.use(async (ctx, next) => {
  console.log('🌐 [网络中间件] 开始处理请求:', ctx.req?.url || 'unknown');

  try {
    await next();
    console.log('🌐 [网络中间件] 请求处理成功');
  } catch (error: any) {
    console.log('🌐 [网络中间件] 捕获到错误:', error);
    console.log('🌐 [网络中间件] 错误类型:', typeof error);
    console.log('🌐 [网络中间件] 错误对象:', error);

    // 网络错误或其他错误
    if (error.response) {
      const { status } = error.response;
      console.log('🌐 [网络中间件] HTTP错误响应，状态码:', status);
      console.log('🌐 [网络中间件] 错误响应对象:', error.response);

      if (status === 401) {
        console.log('🔐 [网络中间件] 处理401网络错误');
        // 检查当前路径，如果是Dashboard相关页面，可能是Token更新的时序问题
        const currentPath = window.location.pathname;
        const isDashboardRelated =
          currentPath.startsWith('/dashboard') ||
          currentPath.startsWith('/team');

        console.log('🔐 [网络中间件] 当前路径:', currentPath);
        console.log('🔐 [网络中间件] 是否Dashboard相关页面:', isDashboardRelated);

        if (isDashboardRelated) {
          console.warn('🔐 [网络中间件] Dashboard页面认证失败，可能是Token更新时序问题');
          // 不立即清除Token和跳转，让页面自己处理
          throw error;
        }

        // 其他页面立即处理认证错误
        console.log('🔐 [网络中间件] 清除Token并显示错误消息');
        TokenManager.clearToken();

        console.log('📢 [网络中间件] 调用message.error() 时间:', new Date().toISOString());
        message.error('登录已过期，请重新登录');

        if (window.location.pathname !== '/user/login') {
          console.log('🔐 [网络中间件] 跳转到登录页');
          history.push('/user/login');
        }
      } else if (status === 403) {
        console.log('🚫 [网络中间件] 处理403网络错误');
        // 检查是否是团队访问被拒绝的特殊错误
        const errorMessage = error.response?.data?.message;
        console.log('🚫 [网络中间件] 错误响应数据:', error.response?.data);
        console.log('🚫 [网络中间件] 提取的错误消息:', errorMessage);

        if (errorMessage?.includes('停用') || errorMessage?.includes('禁用') || errorMessage?.includes('不是该团队的成员')) {
          console.log('🚫 [网络中间件] 团队访问相关错误');
          console.log('📢 [网络中间件] 调用message.error() 时间:', new Date().toISOString());
          console.log('📢 [网络中间件] 显示团队错误消息:', errorMessage);
          // 团队访问相关的错误，使用后端返回的具体消息
          message.error(errorMessage);
        } else {
          console.log('🚫 [网络中间件] 其他权限错误');
          console.log('📢 [网络中间件] 调用message.error() 时间:', new Date().toISOString());
          console.log('📢 [网络中间件] 显示默认权限错误消息');
          // 其他权限错误
          message.error('没有权限访问该资源');
        }
      } else if (status === 404) {
        console.log('📢 [网络中间件] 调用message.error() 时间:', new Date().toISOString());
        message.error('请求的资源不存在');
      } else if (status >= 500) {
        console.log('📢 [网络中间件] 调用message.error() 时间:', new Date().toISOString());
        message.error('服务器错误，请稍后重试');
      } else {
        console.log('📢 [网络中间件] 调用message.error() 时间:', new Date().toISOString());
        message.error(`请求失败: ${status}`);
      }
    } else if (error.request) {
      console.log('🌐 [网络中间件] 网络连接错误');
      console.log('🌐 [网络中间件] 请求对象:', error.request);
      console.log('📢 [网络中间件] 调用message.error() 时间:', new Date().toISOString());
      // 网络连接错误
      message.error('网络错误，请检查网络连接');
    } else {
      console.log('🌐 [网络中间件] 其他类型错误');
      console.log('📢 [网络中间件] 调用message.error() 时间:', new Date().toISOString());
      // 其他错误
      message.error('请求失败，请重试');
    }

    console.log('🌐 [网络中间件] 重新抛出错误');
    throw error;
  }
});

// 封装常用的请求方法
export const apiRequest = {
  get: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.get(url, { params });
  },

  post: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.post(url, { data });
  },

  put: <T = any>(url: string, data?: any): Promise<ApiResponse<T>> => {
    return request.put(url, { data });
  },

  delete: <T = any>(url: string, params?: any): Promise<ApiResponse<T>> => {
    return request.delete(url, { params });
  },
};

// 导出 Token 管理器
export { TokenManager };

// 导出默认请求实例
export default request;
