globalThis.makoModuleHotUpdate('src/pages/test-interceptor.tsx', {
    modules: {
        "src/.umi/core/route.tsx": function(module, exports, __mako_require__) {
            "use strict";
            var interop = __mako_require__("@swc/helpers/_/_interop_require_wildcard")._;
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "getRoutes", {
                enumerable: true,
                get: function() {
                    return getRoutes;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _react = _interop_require_default._(__mako_require__("node_modules/react/index.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            async function getRoutes() {
                const routes = {
                    "1": {
                        "path": "/user",
                        "layout": false,
                        "id": "1"
                    },
                    "2": {
                        "name": "login",
                        "path": "/user/login",
                        "parentId": "1",
                        "id": "2"
                    },
                    "3": {
                        "path": "/invite/:token",
                        "name": "团队邀请",
                        "layout": false,
                        "id": "3"
                    },
                    "4": {
                        "path": "/dashboard",
                        "name": "仪表盘",
                        "icon": "dashboard",
                        "parentId": "ant-design-pro-layout",
                        "id": "4"
                    },
                    "5": {
                        "path": "/team-management",
                        "name": "团队管理",
                        "icon": "team",
                        "parentId": "ant-design-pro-layout",
                        "id": "5"
                    },
                    "6": {
                        "path": "/team",
                        "name": "我的团队",
                        "icon": "team",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "6"
                    },
                    "7": {
                        "path": "/team",
                        "redirect": "/team/list",
                        "parentId": "6",
                        "id": "7"
                    },
                    "8": {
                        "path": "/team/list",
                        "parentId": "6",
                        "id": "8"
                    },
                    "9": {
                        "path": "/team/detail",
                        "parentId": "6",
                        "id": "9"
                    },
                    "10": {
                        "path": "/personal-center",
                        "name": "个人中心",
                        "icon": "user",
                        "layout": false,
                        "id": "10"
                    },
                    "11": {
                        "path": "/user-manage",
                        "name": "用户管理",
                        "icon": "user",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "11"
                    },
                    "12": {
                        "path": "/subscription",
                        "name": "订阅管理",
                        "icon": "crown",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "12"
                    },
                    "13": {
                        "path": "/user/invitations",
                        "name": "我的邀请",
                        "icon": "mail",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "13"
                    },
                    "14": {
                        "path": "/help",
                        "name": "帮助中心",
                        "icon": "question",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "14"
                    },
                    "15": {
                        "path": "/test-interceptor",
                        "name": "拦截器测试",
                        "icon": "experiment",
                        "hideInMenu": true,
                        "parentId": "ant-design-pro-layout",
                        "id": "15"
                    },
                    "16": {
                        "path": "/",
                        "redirect": "/dashboard",
                        "parentId": "ant-design-pro-layout",
                        "id": "16"
                    },
                    "17": {
                        "path": "*",
                        "layout": false,
                        "id": "17"
                    },
                    "ant-design-pro-layout": {
                        "id": "ant-design-pro-layout",
                        "path": "/",
                        "isLayout": true
                    }
                };
                return {
                    routes,
                    routeComponents: {
                        '1': _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/core/EmptyRoute.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/core/EmptyRoute.tsx")))),
                        '2': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/user/login/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/user/login/index.tsx")))),
                        '3': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/invite/[token].tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/invite/[token].tsx")))),
                        '4': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/Dashboard/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/Dashboard/index.tsx")))),
                        '5': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/team-management/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/team-management/index.tsx")))),
                        '6': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/team/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/team/index.tsx")))),
                        '7': _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/core/EmptyRoute.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/core/EmptyRoute.tsx")))),
                        '8': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/team/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/team/index.tsx")))),
                        '9': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/team/detail/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/team/detail/index.tsx")))),
                        '10': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/personal-center/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/personal-center/index.tsx")))),
                        '11': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/user/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/user/index.tsx")))),
                        '12': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/subscription/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/subscription/index.tsx")))),
                        '13': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/user/invitations/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/user/invitations/index.tsx")))),
                        '14': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/help/index.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/help/index.tsx")))),
                        '15': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/test-interceptor.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/test-interceptor.tsx")))),
                        '16': _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/core/EmptyRoute.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/core/EmptyRoute.tsx")))),
                        '17': _react.default.lazy(()=>__mako_require__.ensure2("src/pages/404.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/pages/404.tsx")))),
                        'ant-design-pro-layout': _react.default.lazy(()=>__mako_require__.ensure2("src/.umi/plugin-layout/Layout.tsx").then(__mako_require__.dr(interop, __mako_require__.bind(__mako_require__, "src/.umi/plugin-layout/Layout.tsx"))))
                    }
                };
            }
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "src/pages/test-interceptor.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _react = _interop_require_default._(__mako_require__("node_modules/react/index.js"));
            var _antd = __mako_require__("node_modules/antd/es/index.js");
            var _request = __mako_require__("src/utils/request.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            const { Title, Text } = _antd.Typography;
            const TestInterceptorPage = ()=>{
                const testSuccess = async ()=>{
                    try {
                        const response = await _request.apiRequest.get('/test/success');
                        console.log('成功响应:', response);
                    } catch (error) {
                        console.error('请求失败:', error);
                    }
                };
                const test403Error = async ()=>{
                    try {
                        const response = await _request.apiRequest.get('/test/403');
                        console.log('不应该到达这里:', response);
                    } catch (error) {
                        console.error('捕获到 403 错误:', error);
                    }
                };
                const test401Error = async ()=>{
                    try {
                        const response = await _request.apiRequest.get('/test/401');
                        console.log('不应该到达这里:', response);
                    } catch (error) {
                        console.error('捕获到 401 错误:', error);
                    }
                };
                const testNetworkError = async ()=>{
                    try {
                        const response = await _request.apiRequest.get('/test/network-error');
                        console.log('不应该到达这里:', response);
                    } catch (error) {
                        console.error('捕获到网络错误:', error);
                    }
                };
                return (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        padding: '24px'
                    },
                    children: [
                        (0, _jsxdevruntime.jsxDEV)(Title, {
                            level: 2,
                            children: "拦截器测试页面"
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 55,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "此页面用于测试请求响应拦截器是否正常工作。请打开浏览器开发者工具查看控制台输出和网络请求。"
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 56,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: "测试按钮",
                            style: {
                                marginTop: '24px'
                            },
                            children: (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                direction: "vertical",
                                size: "middle",
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        onClick: testSuccess,
                                        children: "测试成功响应 (200)"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 62,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        danger: true,
                                        onClick: test403Error,
                                        children: "测试 403 权限错误"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 66,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        danger: true,
                                        onClick: test401Error,
                                        children: "测试 401 认证错误"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 70,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        danger: true,
                                        onClick: testNetworkError,
                                        children: "测试网络错误"
                                    }, void 0, false, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 74,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 61,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 60,
                            columnNumber: 7
                        }, this),
                        (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                            title: "预期行为",
                            style: {
                                marginTop: '24px'
                            },
                            children: (0, _jsxdevruntime.jsxDEV)("ul", {
                                children: [
                                    (0, _jsxdevruntime.jsxDEV)("li", {
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)("strong", {
                                                children: "403 错误"
                                            }, void 0, false, {
                                                fileName: "src/pages/test-interceptor.tsx",
                                                lineNumber: 82,
                                                columnNumber: 15
                                            }, this),
                                            ': 应该显示错误消息 "您的账户已在此团队中被停用" 或类似的权限错误消息'
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 82,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)("li", {
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)("strong", {
                                                children: "401 错误"
                                            }, void 0, false, {
                                                fileName: "src/pages/test-interceptor.tsx",
                                                lineNumber: 83,
                                                columnNumber: 15
                                            }, this),
                                            ': 应该显示 "登录已过期，请重新登录" 并跳转到登录页'
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 83,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)("li", {
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)("strong", {
                                                children: "网络错误"
                                            }, void 0, false, {
                                                fileName: "src/pages/test-interceptor.tsx",
                                                lineNumber: 84,
                                                columnNumber: 15
                                            }, this),
                                            ": 应该显示相应的网络错误消息"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 84,
                                        columnNumber: 11
                                    }, this),
                                    (0, _jsxdevruntime.jsxDEV)("li", {
                                        children: [
                                            (0, _jsxdevruntime.jsxDEV)("strong", {
                                                children: "成功响应"
                                            }, void 0, false, {
                                                fileName: "src/pages/test-interceptor.tsx",
                                                lineNumber: 85,
                                                columnNumber: 15
                                            }, this),
                                            ": 正常处理，不显示错误消息"
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/test-interceptor.tsx",
                                        lineNumber: 85,
                                        columnNumber: 11
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/test-interceptor.tsx",
                                lineNumber: 81,
                                columnNumber: 9
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/test-interceptor.tsx",
                            lineNumber: 80,
                            columnNumber: 7
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/test-interceptor.tsx",
                    lineNumber: 54,
                    columnNumber: 5
                }, this);
            };
            _c = TestInterceptorPage;
            var _default = TestInterceptorPage;
            var _c;
            $RefreshReg$(_c, "TestInterceptorPage");
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '938994922064949733';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/test-interceptor.tsx": [
            "src/pages/test-interceptor.tsx"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=src_pages_test-interceptor_tsx-async.14378165467106314185.hot-update.js.map