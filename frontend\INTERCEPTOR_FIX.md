# 响应拦截器修复说明

## 问题描述

`frontend/src/utils/request.ts` 中的请求响应拦截器无法正常工作。当 API 返回 403 状态码时，应该使用 `message.error(data.message || '没有权限访问该资源');` 显示错误消息，但目前没有向用户显示任何错误消息。

API 返回的实际响应结构：
```json
{
  "code": 403,
  "message": "您的账户已在此团队中被停用",
  "data": null,
  "timestamp": "2025-07-31 15:36:25"
}
```

## 问题根本原因

1. **umi-request 响应拦截器语法错误**：原代码使用了类似 axios 的双回调语法 `(successHandler, errorHandler)`，但 umi-request 只支持单个响应处理函数。

2. **错误处理机制不匹配**：umi-request 的响应拦截器需要通过 `throw` 抛出错误，而不是 `return Promise.reject()`。

3. **后端统一返回 HTTP 200**：后端所有响应都返回 HTTP 200 状态码，业务状态码在响应体的 `code` 字段中，这要求拦截器正确解析响应体。

## 修复方案

### 1. 修正响应拦截器语法

**修复前：**
```typescript
request.interceptors.response.use(
  async (response) => {
    // 处理逻辑
    return Promise.reject(new Error(data.message));
  },
  (error: any) => {
    // 错误处理
    return Promise.reject(error);
  }
);
```

**修复后：**
```typescript
request.interceptors.response.use(async (response) => {
  let data;
  try {
    data = await response.clone().json();
  } catch (error) {
    return response;
  }

  if (data.code !== 200) {
    if (data.code === 403) {
      message.error(data.message || '没有权限访问该资源');
      throw new Error(data.message);
    }
    // 其他错误处理...
  }

  return response;
});
```

### 2. 添加网络错误处理中间件

使用 umi-request 的中间件机制处理网络层错误：

```typescript
request.use(async (ctx, next) => {
  try {
    await next();
  } catch (error: any) {
    if (error.response) {
      const { status } = error.response;
      if (status === 403) {
        const errorMessage = error.response?.data?.message;
        message.error(errorMessage || '没有权限访问该资源');
      }
      // 其他状态码处理...
    }
    throw error;
  }
});
```

### 3. 关键修复点

1. **移除双回调语法**：umi-request 响应拦截器只接受一个处理函数
2. **使用 throw 抛出错误**：替代 `Promise.reject()`
3. **正确解析响应体**：使用 `response.clone().json()` 解析业务数据
4. **分层错误处理**：响应拦截器处理业务错误，中间件处理网络错误

## 测试验证

创建了测试页面 `/test-interceptor` 用于验证修复效果：

1. **403 错误测试**：应显示具体的权限错误消息
2. **401 错误测试**：应显示登录过期消息并跳转
3. **网络错误测试**：应显示网络错误消息
4. **成功响应测试**：正常处理，无错误消息

## 访问测试页面

启动前端开发服务器后，访问：
```
http://localhost:8002/test-interceptor
```

## 修复文件

- `frontend/src/utils/request.ts` - 主要修复文件
- `frontend/src/pages/test-interceptor.tsx` - 测试页面
- `frontend/config/routes.ts` - 添加测试路由

## 注意事项

1. 测试页面仅用于开发环境验证，生产环境应移除
2. 修复后的拦截器兼容 umi-request 的洋葱模型架构
3. 保持了原有的 Dashboard 页面特殊处理逻辑
4. 错误消息显示使用 Antd 的 message 组件

## 验证步骤

1. 启动前端开发服务器：`npm run dev`
2. 访问测试页面：`http://localhost:8002/test-interceptor`
3. 点击 "测试 403 权限错误" 按钮
4. 观察是否显示错误消息：`message.error()` 弹出提示
5. 检查浏览器控制台是否有相关日志

修复完成后，403 错误响应应该能够正确触发 `message.error()` 显示错误消息给用户。
