globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/.umi/plugin-layout/icons.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _DashboardOutlined = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/DashboardOutlined.js"));
            var _TeamOutlined = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/TeamOutlined.js"));
            var _UserOutlined = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/UserOutlined.js"));
            var _CrownOutlined = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/CrownOutlined.js"));
            var _MailOutlined = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/MailOutlined.js"));
            var _QuestionOutlined = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/QuestionOutlined.js"));
            var _ExperimentOutlined = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/ExperimentOutlined.js"));
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            var _default = {
                DashboardOutlined: _DashboardOutlined.default,
                TeamOutlined: _TeamOutlined.default,
                UserOutlined: _UserOutlined.default,
                CrownOutlined: _CrownOutlined.default,
                MailOutlined: _MailOutlined.default,
                QuestionOutlined: _QuestionOutlined.default,
                ExperimentOutlined: _ExperimentOutlined.default
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        },
        "node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/icons/ExperimentOutlined.js": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            __mako_require__.d(exports, "default", {
                enumerable: true,
                get: function() {
                    return _default;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _objectSpread2 = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/@babel/runtime/helpers/esm/objectSpread2.js"));
            var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
            var _ExperimentOutlined = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/@ant-design/icons-svg/es/asn/ExperimentOutlined.js"));
            var _AntdIcon = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/@umijs/plugins/node_modules/@ant-design/icons/es/components/AntdIcon.js"));
            var ExperimentOutlined = function ExperimentOutlined(props, ref) {
                return /*#__PURE__*/ _react.createElement(_AntdIcon.default, (0, _objectSpread2.default)((0, _objectSpread2.default)({}, props), {}, {
                    ref: ref,
                    icon: _ExperimentOutlined.default
                }));
            };
            var RefIcon = /*#__PURE__*/ _react.forwardRef(ExperimentOutlined);
            RefIcon.displayName = 'ExperimentOutlined';
            var _default = RefIcon;
        }
    }
}, function(runtime) {
    runtime._h = '17706288296718424449';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/test-interceptor.tsx": [
            "src/pages/test-interceptor.tsx"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.938994922064949733.hot-update.js.map