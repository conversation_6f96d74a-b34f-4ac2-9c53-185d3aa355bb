{"version": 3, "sources": ["umi.14378165467106314185.hot-update.js", "src/.umi/core/route.tsx", "src/pages/test-interceptor.tsx"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='938994922064949733';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/test-interceptor.tsx\":[\"src/pages/test-interceptor.tsx\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\n\nexport async function getRoutes() {\n  const routes = {\"1\":{\"path\":\"/user\",\"layout\":false,\"id\":\"1\"},\"2\":{\"name\":\"login\",\"path\":\"/user/login\",\"parentId\":\"1\",\"id\":\"2\"},\"3\":{\"path\":\"/invite/:token\",\"name\":\"团队邀请\",\"layout\":false,\"id\":\"3\"},\"4\":{\"path\":\"/dashboard\",\"name\":\"仪表盘\",\"icon\":\"dashboard\",\"parentId\":\"ant-design-pro-layout\",\"id\":\"4\"},\"5\":{\"path\":\"/team-management\",\"name\":\"团队管理\",\"icon\":\"team\",\"parentId\":\"ant-design-pro-layout\",\"id\":\"5\"},\"6\":{\"path\":\"/team\",\"name\":\"我的团队\",\"icon\":\"team\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"6\"},\"7\":{\"path\":\"/team\",\"redirect\":\"/team/list\",\"parentId\":\"6\",\"id\":\"7\"},\"8\":{\"path\":\"/team/list\",\"parentId\":\"6\",\"id\":\"8\"},\"9\":{\"path\":\"/team/detail\",\"parentId\":\"6\",\"id\":\"9\"},\"10\":{\"path\":\"/personal-center\",\"name\":\"个人中心\",\"icon\":\"user\",\"layout\":false,\"id\":\"10\"},\"11\":{\"path\":\"/user-manage\",\"name\":\"用户管理\",\"icon\":\"user\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"11\"},\"12\":{\"path\":\"/subscription\",\"name\":\"订阅管理\",\"icon\":\"crown\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"12\"},\"13\":{\"path\":\"/user/invitations\",\"name\":\"我的邀请\",\"icon\":\"mail\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"13\"},\"14\":{\"path\":\"/help\",\"name\":\"帮助中心\",\"icon\":\"question\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"14\"},\"15\":{\"path\":\"/test-interceptor\",\"name\":\"拦截器测试\",\"icon\":\"experiment\",\"hideInMenu\":true,\"parentId\":\"ant-design-pro-layout\",\"id\":\"15\"},\"16\":{\"path\":\"/\",\"redirect\":\"/dashboard\",\"parentId\":\"ant-design-pro-layout\",\"id\":\"16\"},\"17\":{\"path\":\"*\",\"layout\":false,\"id\":\"17\"},\"ant-design-pro-layout\":{\"id\":\"ant-design-pro-layout\",\"path\":\"/\",\"isLayout\":true}} as const;\n  return {\n    routes,\n    routeComponents: {\n'1': React.lazy(() => import('./EmptyRoute')),\n'2': React.lazy(() => import(/* webpackChunkName: \"p__user__login__index\" */'@/pages/user/login/index.tsx')),\n'3': React.lazy(() => import(/* webpackChunkName: \"p__invite__token\" */'@/pages/invite/[token].tsx')),\n'4': React.lazy(() => import(/* webpackChunkName: \"p__Dashboard__index\" */'@/pages/Dashboard/index.tsx')),\n'5': React.lazy(() => import(/* webpackChunkName: \"p__team-management__index\" */'@/pages/team-management/index.tsx')),\n'6': React.lazy(() => import(/* webpackChunkName: \"p__team__index\" */'@/pages/team/index.tsx')),\n'7': React.lazy(() => import('./EmptyRoute')),\n'8': React.lazy(() => import(/* webpackChunkName: \"p__team__index\" */'@/pages/team/index.tsx')),\n'9': React.lazy(() => import(/* webpackChunkName: \"p__team__detail__index\" */'@/pages/team/detail/index.tsx')),\n'10': React.lazy(() => import(/* webpackChunkName: \"p__personal-center__index\" */'@/pages/personal-center/index.tsx')),\n'11': React.lazy(() => import(/* webpackChunkName: \"p__user__index\" */'@/pages/user/index.tsx')),\n'12': React.lazy(() => import(/* webpackChunkName: \"p__subscription__index\" */'@/pages/subscription/index.tsx')),\n'13': React.lazy(() => import(/* webpackChunkName: \"p__user__invitations__index\" */'@/pages/user/invitations/index.tsx')),\n'14': React.lazy(() => import(/* webpackChunkName: \"p__help__index\" */'@/pages/help/index.tsx')),\n'15': React.lazy(() => import(/* webpackChunkName: \"p__test-interceptor\" */'@/pages/test-interceptor.tsx')),\n'16': React.lazy(() => import('./EmptyRoute')),\n'17': React.lazy(() => import(/* webpackChunkName: \"p__404\" */'@/pages/404.tsx')),\n'ant-design-pro-layout': React.lazy(() => import(/* webpackChunkName: \"umi__plugin-layout__Layout\" */'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-layout/Layout.tsx')),\n},\n  };\n}\n", "/**\n * 测试拦截器页面\n * 用于测试响应拦截器是否正常工作\n */\n\nimport React from 'react';\nimport { <PERSON>ton, Card, Space, Typography } from 'antd';\nimport { apiRequest } from '@/utils/request';\n\nconst { Title, Text } = Typography;\n\nconst TestInterceptorPage: React.FC = () => {\n  const testSuccess = async () => {\n    try {\n      // 测试成功响应\n      const response = await apiRequest.get('/test/success');\n      console.log('成功响应:', response);\n    } catch (error) {\n      console.error('请求失败:', error);\n    }\n  };\n\n  const test403Error = async () => {\n    try {\n      // 测试 403 错误响应\n      const response = await apiRequest.get('/test/403');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到 403 错误:', error);\n    }\n  };\n\n  const test401Error = async () => {\n    try {\n      // 测试 401 错误响应\n      const response = await apiRequest.get('/test/401');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到 401 错误:', error);\n    }\n  };\n\n  const testNetworkError = async () => {\n    try {\n      // 测试网络错误\n      const response = await apiRequest.get('/test/network-error');\n      console.log('不应该到达这里:', response);\n    } catch (error) {\n      console.error('捕获到网络错误:', error);\n    }\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Title level={2}>拦截器测试页面</Title>\n      <Text type=\"secondary\">\n        此页面用于测试请求响应拦截器是否正常工作。请打开浏览器开发者工具查看控制台输出和网络请求。\n      </Text>\n\n      <Card title=\"测试按钮\" style={{ marginTop: '24px' }}>\n        <Space direction=\"vertical\" size=\"middle\">\n          <Button type=\"primary\" onClick={testSuccess}>\n            测试成功响应 (200)\n          </Button>\n          \n          <Button danger onClick={test403Error}>\n            测试 403 权限错误\n          </Button>\n          \n          <Button danger onClick={test401Error}>\n            测试 401 认证错误\n          </Button>\n          \n          <Button danger onClick={testNetworkError}>\n            测试网络错误\n          </Button>\n        </Space>\n      </Card>\n\n      <Card title=\"预期行为\" style={{ marginTop: '24px' }}>\n        <ul>\n          <li><strong>403 错误</strong>: 应该显示错误消息 \"您的账户已在此团队中被停用\" 或类似的权限错误消息</li>\n          <li><strong>401 错误</strong>: 应该显示 \"登录已过期，请重新登录\" 并跳转到登录页</li>\n          <li><strong>网络错误</strong>: 应该显示相应的网络错误消息</li>\n          <li><strong>成功响应</strong>: 正常处理，不显示错误消息</li>\n        </ul>\n      </Card>\n    </div>\n  );\n};\n\nexport default TestInterceptorPage;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;wCCES;;;2BAAA;;;;;;mFAFJ;;;;;;;;;YAEX,eAAe;gBACpB,MAAM,SAAS;oBAAC,KAAI;wBAAC,QAAO;wBAAQ,UAAS;wBAAM,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAQ,QAAO;wBAAc,YAAW;wBAAI,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAiB,QAAO;wBAAO,UAAS;wBAAM,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAa,QAAO;wBAAM,QAAO;wBAAY,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAmB,QAAO;wBAAO,QAAO;wBAAO,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAQ,QAAO;wBAAO,QAAO;wBAAO,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAQ,YAAW;wBAAa,YAAW;wBAAI,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAa,YAAW;wBAAI,MAAK;oBAAG;oBAAE,KAAI;wBAAC,QAAO;wBAAe,YAAW;wBAAI,MAAK;oBAAG;oBAAE,MAAK;wBAAC,QAAO;wBAAmB,QAAO;wBAAO,QAAO;wBAAO,UAAS;wBAAM,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAe,QAAO;wBAAO,QAAO;wBAAO,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAgB,QAAO;wBAAO,QAAO;wBAAQ,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAoB,QAAO;wBAAO,QAAO;wBAAO,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAQ,QAAO;wBAAO,QAAO;wBAAW,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAoB,QAAO;wBAAQ,QAAO;wBAAa,cAAa;wBAAK,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAI,YAAW;wBAAa,YAAW;wBAAwB,MAAK;oBAAI;oBAAE,MAAK;wBAAC,QAAO;wBAAI,UAAS;wBAAM,MAAK;oBAAI;oBAAE,yBAAwB;wBAAC,MAAK;wBAAwB,QAAO;wBAAI,YAAW;oBAAI;gBAAC;gBAC5jD,OAAO;oBACL;oBACA,iBAAiB;wBACrB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,mBAAK,cAAK,CAAC,IAAI,CAAC,IAAM;wBACtB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,oBAAM,cAAK,CAAC,IAAI,CAAC,IAAM;wBACvB,uCAAyB,cAAK,CAAC,IAAI,CAAC,IAAM;oBAC1C;gBACE;YACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCC6DA;;;2BAAA;;;;;;;mFAtFkB;yCAC8B;4CACrB;;;;;;;;;YAE3B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;YAElC,MAAM,sBAAgC;gBACpC,MAAM,cAAc;oBAClB,IAAI;wBACF,SAAS;wBACT,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;wBACtC,QAAQ,GAAG,CAAC,SAAS;oBACvB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,SAAS;oBACzB;gBACF;gBAEA,MAAM,eAAe;oBACnB,IAAI;wBACF,cAAc;wBACd,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;wBACtC,QAAQ,GAAG,CAAC,YAAY;oBAC1B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,eAAe;oBAC/B;gBACF;gBAEA,MAAM,eAAe;oBACnB,IAAI;wBACF,cAAc;wBACd,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;wBACtC,QAAQ,GAAG,CAAC,YAAY;oBAC1B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,eAAe;oBAC/B;gBACF;gBAEA,MAAM,mBAAmB;oBACvB,IAAI;wBACF,SAAS;wBACT,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAC;wBACtC,QAAQ,GAAG,CAAC,YAAY;oBAC1B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,YAAY;oBAC5B;gBACF;gBAEA,qBACE,2BAAC;oBAAI,OAAO;wBAAE,SAAS;oBAAO;;sCAC5B,2BAAC;4BAAM,OAAO;sCAAG;;;;;;sCACjB,2BAAC;4BAAK,MAAK;sCAAY;;;;;;sCAIvB,2BAAC,UAAI;4BAAC,OAAM;4BAAO,OAAO;gCAAE,WAAW;4BAAO;sCAC5C,cAAA,2BAAC,WAAK;gCAAC,WAAU;gCAAW,MAAK;;kDAC/B,2BAAC,YAAM;wCAAC,MAAK;wCAAU,SAAS;kDAAa;;;;;;kDAI7C,2BAAC,YAAM;wCAAC,MAAM;wCAAC,SAAS;kDAAc;;;;;;kDAItC,2BAAC,YAAM;wCAAC,MAAM;wCAAC,SAAS;kDAAc;;;;;;kDAItC,2BAAC,YAAM;wCAAC,MAAM;wCAAC,SAAS;kDAAkB;;;;;;;;;;;;;;;;;sCAM9C,2BAAC,UAAI;4BAAC,OAAM;4BAAO,OAAO;gCAAE,WAAW;4BAAO;sCAC5C,cAAA,2BAAC;;kDACC,2BAAC;;0DAAG,2BAAC;0DAAO;;;;;;4CAAe;;;;;;;kDAC3B,2BAAC;;0DAAG,2BAAC;0DAAO;;;;;;4CAAe;;;;;;;kDAC3B,2BAAC;;0DAAG,2BAAC;0DAAO;;;;;;4CAAa;;;;;;;kDACzB,2BAAC;;0DAAG,2BAAC;0DAAO;;;;;;4CAAa;;;;;;;;;;;;;;;;;;;;;;;;YAKnC;iBA9EM;gBAgFN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;IFxFD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,kCAAiC;YAAC;SAAiC;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AACn9B"}