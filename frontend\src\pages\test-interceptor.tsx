/**
 * 测试拦截器页面
 * 用于测试响应拦截器是否正常工作
 */

import React from 'react';
import { Button, Card, Space, Typography } from 'antd';
import { apiRequest } from '@/utils/request';

const { Title, Text } = Typography;

const TestInterceptorPage: React.FC = () => {
  const testSuccess = async () => {
    try {
      // 测试成功响应
      const response = await apiRequest.get('/test/success');
      console.log('成功响应:', response);
    } catch (error) {
      console.error('请求失败:', error);
    }
  };

  const test403Error = async () => {
    try {
      // 测试 403 错误响应 - 使用一个可能返回403的真实API
      const response = await apiRequest.get('/api/teams/999999/members');
      console.log('不应该到达这里:', response);
    } catch (error) {
      console.error('捕获到 403 错误:', error);
    }
  };

  const testRealTeamAccess = async () => {
    try {
      // 测试真实的团队访问，可能触发403错误
      console.log('🧪 [测试] 开始测试真实团队访问...');
      const response = await apiRequest.get('/api/teams/current');
      console.log('✅ [测试] 团队访问成功:', response);
    } catch (error) {
      console.error('❌ [测试] 团队访问失败:', error);
    }
  };

  const test401Error = async () => {
    try {
      // 测试 401 错误响应
      const response = await apiRequest.get('/test/401');
      console.log('不应该到达这里:', response);
    } catch (error) {
      console.error('捕获到 401 错误:', error);
    }
  };

  const testNetworkError = async () => {
    try {
      // 测试网络错误
      const response = await apiRequest.get('/test/network-error');
      console.log('不应该到达这里:', response);
    } catch (error) {
      console.error('捕获到网络错误:', error);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>拦截器测试页面</Title>
      <Text type="secondary">
        此页面用于测试请求响应拦截器是否正常工作。请打开浏览器开发者工具查看控制台输出和网络请求。
      </Text>

      <Card title="测试按钮" style={{ marginTop: '24px' }}>
        <Space direction="vertical" size="middle">
          <Button type="primary" onClick={testSuccess}>
            测试成功响应 (200)
          </Button>

          <Button danger onClick={test403Error}>
            测试 403 权限错误 (模拟)
          </Button>

          <Button danger onClick={testRealTeamAccess}>
            测试真实团队访问 (可能403)
          </Button>

          <Button danger onClick={test401Error}>
            测试 401 认证错误
          </Button>

          <Button danger onClick={testNetworkError}>
            测试网络错误
          </Button>
        </Space>
      </Card>

      <Card title="预期行为" style={{ marginTop: '24px' }}>
        <ul>
          <li><strong>403 错误</strong>: 应该显示错误消息 "您的账户已在此团队中被停用" 或类似的权限错误消息</li>
          <li><strong>401 错误</strong>: 应该显示 "登录已过期，请重新登录" 并跳转到登录页</li>
          <li><strong>网络错误</strong>: 应该显示相应的网络错误消息</li>
          <li><strong>成功响应</strong>: 正常处理，不显示错误消息</li>
        </ul>
      </Card>
    </div>
  );
};

export default TestInterceptorPage;
