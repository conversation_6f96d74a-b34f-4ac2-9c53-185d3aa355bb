/**
 * 测试拦截器页面
 * 用于测试响应拦截器是否正常工作
 */

import React from 'react';
import { Button, Card, Space, Typography } from 'antd';
import { apiRequest } from '@/utils/request';

const { Title, Text } = Typography;

const TestInterceptorPage: React.FC = () => {
  const testSuccess = async () => {
    try {
      // 测试成功响应
      const response = await apiRequest.get('/test/success');
      console.log('成功响应:', response);
    } catch (error) {
      console.error('请求失败:', error);
    }
  };

  const test403Error = async () => {
    try {
      // 测试 403 错误响应 - 使用一个可能返回403的真实API
      const response = await apiRequest.get('/api/teams/999999/members');
      console.log('不应该到达这里:', response);
    } catch (error) {
      console.error('捕获到 403 错误:', error);
    }
  };

  const testRealTeamAccess = async () => {
    try {
      // 测试真实的团队访问，可能触发403错误
      console.log('🧪 [测试] 开始测试真实团队访问...');
      const response = await apiRequest.get('/api/teams/current');
      console.log('✅ [测试] 团队访问成功:', response);
    } catch (error) {
      console.error('❌ [测试] 团队访问失败:', error);
    }
  };

  const testMock403 = async () => {
    try {
      console.log('🧪 [测试] 开始模拟403错误响应...');

      // 创建一个模拟的403响应
      const mockResponse = new Response(
        JSON.stringify({
          code: 403,
          message: "您的账户已在此团队中被停用",
          data: null,
          timestamp: new Date().toISOString()
        }),
        {
          status: 200, // 后端返回HTTP 200，业务状态码在body中
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      // 手动触发响应拦截器
      console.log('🧪 [测试] 手动触发响应拦截器...');
      // 这里我们无法直接调用拦截器，所以改为测试真实API
      const response = await apiRequest.get('/api/teams/999999/info');
      console.log('不应该到达这里:', response);
    } catch (error) {
      console.error('❌ [测试] 模拟403测试结果:', error);
    }
  };

  const test401Error = async () => {
    try {
      // 测试 401 错误响应
      const response = await apiRequest.get('/test/401');
      console.log('不应该到达这里:', response);
    } catch (error) {
      console.error('捕获到 401 错误:', error);
    }
  };

  const testNetworkError = async () => {
    try {
      // 测试网络错误
      const response = await apiRequest.get('/test/network-error');
      console.log('不应该到达这里:', response);
    } catch (error) {
      console.error('捕获到网络错误:', error);
    }
  };

  const testMessageComponent = () => {
    // 直接测试 message 组件是否正常工作
    console.log('🧪 [测试] 直接测试 message 组件...');
    message.success('✅ Message 组件测试 - 成功消息');
    setTimeout(() => {
      message.error('❌ Message 组件测试 - 错误消息');
    }, 1000);
    setTimeout(() => {
      message.warning('⚠️ Message 组件测试 - 警告消息');
    }, 2000);
    setTimeout(() => {
      message.info('ℹ️ Message 组件测试 - 信息消息');
    }, 3000);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>拦截器测试页面</Title>
      <Text type="secondary">
        此页面用于测试请求响应拦截器是否正常工作。请打开浏览器开发者工具查看控制台输出和网络请求。
      </Text>

      <Card title="测试按钮" style={{ marginTop: '24px' }}>
        <Space direction="vertical" size="middle">
          <Button type="primary" onClick={testMessageComponent}>
            🧪 测试 Message 组件 (直接测试)
          </Button>

          <Button type="primary" onClick={testSuccess}>
            测试成功响应 (200)
          </Button>

          <Button danger onClick={test403Error}>
            测试 403 权限错误 (模拟API)
          </Button>

          <Button danger onClick={testMock403}>
            测试 403 错误 (不存在的团队)
          </Button>

          <Button danger onClick={testRealTeamAccess}>
            测试真实团队访问 (可能403)
          </Button>

          <Button danger onClick={test401Error}>
            测试 401 认证错误
          </Button>

          <Button danger onClick={testNetworkError}>
            测试网络错误
          </Button>
        </Space>
      </Card>

      <Card title="预期行为" style={{ marginTop: '24px' }}>
        <ul>
          <li><strong>Message 组件测试</strong>: 应该依次显示成功、错误、警告、信息四种类型的消息提示</li>
          <li><strong>403 错误</strong>: 应该显示错误消息 "您的账户已在此团队中被停用" 或类似的权限错误消息</li>
          <li><strong>401 错误</strong>: 应该显示 "登录已过期，请重新登录" 并跳转到登录页</li>
          <li><strong>网络错误</strong>: 应该显示相应的网络错误消息</li>
          <li><strong>成功响应</strong>: 正常处理，不显示错误消息</li>
        </ul>
      </Card>

      <Card title="🔧 修复说明" style={{ marginTop: '24px' }}>
        <p><strong>根本问题</strong>: Antd 5.x 中的 message 组件需要通过 App 组件来正确工作。</p>
        <p><strong>解决方案</strong>: 已在 config.ts 中启用 appConfig 配置，现在 message 组件应该能正常显示。</p>
        <p><strong>测试方法</strong>: 先点击 "测试 Message 组件" 按钮验证基础功能，再测试拦截器错误处理。</p>
      </Card>
    </div>
  );
};

export default TestInterceptorPage;
